"""add_order_id_column

Revision ID: 9084abb52d93
Revises: 33e616e7dd03
Create Date: 2025-07-07 14:57:02.997812

"""

import random
from datetime import datetime

import sqlalchemy as sa
from alembic import op


def generate_order_id():
    now = datetime.utcnow().strftime("%Y%m%d%H%M%S")
    rand = random.randint(100000, 999999)  # Generate a random number
    return f"ORD-{now}-{rand}"


# revision identifiers, used by Alembic.
revision = "9084abb52d93"
down_revision = "33e616e7dd03"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "orders",
        sa.Column("order_id", sa.String(length=32), nullable=True),
        schema="orders",
    )
    op.create_unique_constraint(
        "uq_orders_order_id", "orders", ["order_id"], schema="orders"
    )
    connection = op.get_bind()
    result = connection.execute(
        sa.text("SELECT id FROM orders.orders WHERE order_id IS NULL")
    )
    # Backfill order_id for existing orders
    for row in result:
        order_id = generate_order_id()
        connection.execute(
            sa.text("UPDATE orders.orders SET order_id = :order_id WHERE id = :id"),
            {"order_id": order_id, "id": row["id"]},
        )

    # Optional: Make order_id non-nullable after backfilling
    op.alter_column(
        "orders",
        "order_id",
        existing_type=sa.String(length=32),
        nullable=False,
        schema="orders",
    )


def downgrade():
    op.drop_constraint("uq_orders_order_id", "orders", schema="orders")
    op.drop_column("orders", "order_id", schema="orders")
