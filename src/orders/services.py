import logging
from abc import ABC, abstractmethod
from typing import List
from uuid import UUID

from pydantic import EmailStr
from sqlalchemy.exc import SQLAlchemyError

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from app.config import logger, settings
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching
from common.types import FormFactor
from mail_delivery.service import AbstractMailService
from orders.adapters.repository import AbstractOrdersRepository
from orders.constants import OrderStatus, RecipientType
from orders.domain import model
from orders.email_template import OrderData, OrderEmailBuilder
from sim.adapters.externalapi import AuditServiceAPI
from sim.domain.model import AccountActivityLog
from sim.exceptions import NotFound


class AbstractOrdersService(ABC):
    @abstractmethod
    def create_order(
        self, order: model.OrderRequest, client_ip: str, user: str | None = None
    ) -> model.OrderResponse:
        ...

    @abstractmethod
    def update_order_status(
        self,
        order_uuid: UUID,
        update_data: UpdateOrderStatusRequest,
        client_ip: str,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        ...

    @abstractmethod
    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[List[model.OrdersData], int]:
        ...

    @abstractmethod
    def get_order_details(self, order_uuid: UUID) -> model.OrderDetailsResponse:
        ...


class OrdersService(AbstractOrdersService):
    def __init__(
        self,
        orders_repository: AbstractOrdersRepository,
        mail_service: AbstractMailService,
        audit_service: AuditServiceAPI,
    ):
        self.orders_repository = orders_repository
        self.mail_service = mail_service
        self.audit_service = audit_service

    def __constructing_order_data_for_mail(self, order_uuid: UUID) -> OrderData:
        order_details = self.orders_repository.get_order_details(order_uuid=order_uuid)
        sim_type_wise_qty: dict = {
            item.sim_type: item.quantity for item in order_details.order_items
        }
        tracking_info = {}
        if order_details.order_tracking is not None:
            tracking_info = {
                "tracking_url": order_details.order_tracking.reference_url,
                "tracking_ref": order_details.order_tracking.reference_id,
            }
        order_data: OrderData = OrderData(
            order_id=str(order_details.order_id),
            order_uuid=str(order_uuid),
            order_status=order_details.status,  # type: ignore
            order_date=order_details.order_date.strftime("%Y-%m-%d %H:%M"),
            sim_type_wise_qty=sim_type_wise_qty,
            customer_account_ref=(
                order_details.customer_details.customer_reference  # type: ignore
            ),  # type: ignore
            customer_contact_name=order_details.shipping_details.contact_name,
            person_name=(
                order_details.customer_details.person_placing_order  # type: ignore
            ),  # type: ignore
            person_email=order_details.customer_details.customer_email,
            phone_number=order_details.customer_details.customer_contact_no,
            shipping_address_address1=order_details.shipping_details.address_line1,
            shipping_address_address2=order_details.shipping_details.address_line2,
            city=order_details.shipping_details.city,
            state=order_details.shipping_details.state_or_region,
            postal_code=order_details.shipping_details.postal_code,
            country=order_details.shipping_details.country,
            additional_info=order_details.shipping_details.other_information,
            comments=order_details.comments,
            web_link=(
                f"{settings.APP_LANDING_PAGE_URL}"
                f"/sim-management?tab=sim-order&page=1&pageSize=10"
                f"&search={str(order_details.order_id)}"
            ),
            **tracking_info,
        )
        return order_data

    def __send_mail(self, order_uuid: UUID):
        order_data: OrderData = self.__constructing_order_data_for_mail(
            order_uuid=order_uuid
        )
        user_email_builder = OrderEmailBuilder(
            order_data=order_data, recipient_type=RecipientType.USER
        )
        manager_email_builder = OrderEmailBuilder(
            order_data=order_data, recipient_type=RecipientType.MANAGER
        )
        try:
            self.mail_service.send_mail(
                subject=user_email_builder.get_subject(),
                name_from="BT IoT Portal",
                recipients=[order_data.person_email],
                html_body=user_email_builder.build_email_body(),
            )
        except Exception as ex:
            logging.error(f"getting Error while sending mail: {ex}")
        if not settings.BT_MANAGER_MAIL_ID:
            raise ValueError(
                "Can not sent mail to BT Manager Please set BT_MANAGER_MAIL_ID"
            )
        if order_data.order_status not in [
            OrderStatus.PENDING.value,
            OrderStatus.CANCELLED.value,
        ]:
            return  # Skip sending to manager
        try:
            self.mail_service.send_mail(
                subject=manager_email_builder.get_subject(),
                name_from="BT IoT Portal",
                recipients=[
                    EmailStr(email) for email in settings.BT_MANAGER_MAIL_ID.split(";")
                ],
                html_body=manager_email_builder.build_email_body(),
            )
        except Exception as ex:
            logging.error(f"getting Error while sending mail: {ex}")

    def create_order(
        self, order: model.OrderRequest, client_ip: str, user: str | None = None
    ) -> model.OrderResponse:
        try:
            for item in order.order_items:
                if item.sim_type in [
                    FormFactor.eSIM_MFF2.value,
                    FormFactor.eSIM_MFF2_eUICC.value,
                ] and (
                    item.quantity is None or item.quantity < settings.MIN_E_SIM_QUANTITY
                ):
                    raise ParsingError(
                        (
                            (
                                f"Minimum allowed quantity for {item.sim_type} is "
                                f"{settings.MIN_E_SIM_QUANTITY}. "
                                f"Requested: {item.quantity}"
                            )
                        )
                    )
            # Adding order and return uuid
            order_response = self.orders_repository.create_order(order=order)
            if not order_response.id:
                logger.error("Failed to create order, no response received.")
                raise ParsingError("Failed to create order, please try again.")
            # Adding order and customer details
            self.orders_repository.add_order_customer(
                customer_details=order.customer_details, order_uuid=order_response.id
            )
            # Adding order shipping details
            self.orders_repository.add_order_shipping(
                shipping_details=order.shipping_details, order_uuid=order_response.id
            )

            # Adding order item
            self.orders_repository.add_order_item(
                order_items=order.order_items, order_uuid=order_response.id
            )

            self.orders_repository.commit_order()
            order_details = self.orders_repository.get_order_details(
                order_uuid=order_response.id
            )
            account_name = order_details.customer_details.customer_account_name
            order_status = order_details.status.capitalize()

            self.audit_service.add_account_audit_log(
                AccountActivityLog(
                    account_id=str(order_details.customer_details.customer_account_id),
                    account_name=account_name,
                    user=order_details.order_by,
                    prior_value="",
                    new_value=f"{order_status} #{order_details.order_id}",
                    prior_text="",
                    new_text="Created",
                    field="SIM Order",
                    action="Created",
                    client_ip=client_ip,
                )
            )
            self.__send_mail(order_uuid=order_response.id)
            return order_response
        except SQLAlchemyError as e:
            logger.error(f"Error creating order: {e}")
            raise

    def update_order_status(
        self,
        order_uuid: UUID,
        update_data: UpdateOrderStatusRequest,
        client_ip: str,
        user: str | None = None,
    ) -> UpdateOrderStatusResponse:
        order = self.orders_repository.get_order(order_uuid)
        # Check if the order exists
        if not order:
            raise NotFound("Data not found.")
        else:
            current_status = order.status
            new_status = update_data.status
        # Define valid status transitions
        allowed_transitions: dict[str, list[str]] = {
            OrderStatus.PENDING.value: [
                OrderStatus.CANCELLED.value,
                OrderStatus.APPROVED.value,
                OrderStatus.ONHOLD.value,
            ],
            OrderStatus.ONHOLD.value: [
                OrderStatus.CANCELLED.value,
                OrderStatus.APPROVED.value,
            ],
            OrderStatus.APPROVED.value: [
                OrderStatus.CANCELLED.value,
                OrderStatus.SHIPPED.value,
            ],
            OrderStatus.SHIPPED.value: [],
        }

        # Validate transition
        if current_status in allowed_transitions:
            if new_status not in allowed_transitions[current_status]:
                raise ParsingError(
                    (
                        f"Invalid status transition from {current_status} "
                        f"to {new_status}"
                    )
                )
        else:
            # If current status is not in allowed transitions
            # disallow changes
            raise ParsingError(
                (
                    f"Cannot update status from {current_status}. "
                    f"Status updates not allowed to {new_status}."
                )
            )

        # Validate tracking info - only allowed for SHIPPED status
        if update_data.status == OrderStatus.SHIPPED.value:
            if not update_data.tracking:
                raise ParsingError(
                    "Tracking information is required for shipped orders"
                )
            if not update_data.tracking.reference_id:
                raise ParsingError(
                    "Tracking reference ID is required for shipped orders"
                )
            if not update_data.tracking.reference_url:
                raise ParsingError(
                    "Tracking reference URL is required for shipped orders"
                )
            self.orders_repository.add_order_tracking(
                order_uuid=order_uuid, tracking=update_data.tracking
            )
        elif update_data.tracking:
            raise ParsingError(
                "Tracking information is only allowed for shipped orders"
            )

        if (
            update_data.status == OrderStatus.CANCELLED.value
            or update_data.status == OrderStatus.ONHOLD.value
        ):
            if not update_data.comments:
                raise ParsingError(
                    f"Comments are required for orders with status {update_data.status}"
                )
            self.orders_repository.add_reject_reason(
                order_uuid=order_uuid, update_data=update_data
            )
        elif update_data.comments:
            raise ParsingError("Comments are only allowed for cancelled orders")

        # Update order status
        order_response = self.orders_repository.update_order_status(
            order_uuid=order_uuid, update_data=update_data
        )
        order_details = self.orders_repository.get_order_details(
            order_uuid=order_response.id
        )
        account_name = order_details.customer_details.customer_account_name
        order_status = order_details.status.capitalize()
        self.audit_service.add_account_audit_log(
            AccountActivityLog(
                account_id=str(order_details.customer_details.customer_account_id),
                account_name=account_name,
                user=order_details.order_by,
                prior_value=f"{current_status.capitalize()} #{order_details.order_id}",
                new_value=f"{order_status} #{order_details.order_id}",
                prior_text=f"{current_status.capitalize()}#{order_details.order_id}",
                new_text=f"{order_status} #{order_details.order_id}",
                field="SIM Order",
                action="Updated",
                client_ip=client_ip,
            )
        )
        self.__send_mail(order_uuid=order_response.id)
        return order_response

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[List[model.OrdersData], int]:

        response = self.orders_repository.get_orders(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        count = self.orders_repository.get_orders_count(
            account_id=account_id,
            searching=searching,
        )

        return response, count

    def get_order_details(self, order_uuid: UUID) -> model.OrderDetailsResponse:
        order_details = self.orders_repository.get_order_details(order_uuid=order_uuid)
        return order_details
