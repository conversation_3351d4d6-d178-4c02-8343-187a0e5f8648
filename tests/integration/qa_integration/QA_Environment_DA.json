{"id": "7d7d6f26-4b35-4173-ba0f-44ec659db62e", "name": "QA_Environment_DA", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2025-06", "type": "default", "enabled": true}, {"key": "account_id", "value": "1", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 462, "type": "default", "enabled": true}, {"key": "rateplanid", "value": 352, "type": "any", "enabled": true}, {"key": "AccountId", "value": 841, "type": "any", "enabled": true}, {"key": "Name", "value": "1u269xjjzl", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "secret", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "secret", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "secret", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "secret", "enabled": true}, {"key": "Password", "value": "P@ssw0rd_1", "type": "secret", "enabled": true}, {"key": "fromDate", "value": "2025-07-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2025-06-29", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": 122, "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2025-06", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-09", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2025-07-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "911451529429790", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2025-07-31", "type": "any", "enabled": true}, {"key": "payment_term", "value": "3", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "61655680746555943715", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "i8oc3v0afr", "type": "any", "enabled": true}, {"key": "resource_id", "value": "6ce1a37f-7572-46ab-8fd4-045d5cc8157a", "type": "any", "enabled": true}, {"key": "role_id", "value": "59ad4de0-8379-4278-8020-e7d783697019", "type": "any", "enabled": true}, {"key": "discription", "value": "biz24n7h9yx5w3i39brf", "type": "any", "enabled": true}, {"key": "permission_id", "value": "58714439-f460-479c-8725-95204e97b2ba", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "4rne7ce9pn", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "y4zi3a7", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "3os6cxq", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "vfe66aw", "type": "any", "enabled": true}, {"key": "permission_name", "value": "hcbqzvwublqu", "type": "default", "enabled": true}, {"key": "scope_id", "value": "adee4485-5a8e-4939-b758-dec0e6418784", "type": "default", "enabled": true}, {"key": "group_id", "value": "99a7fdc4-39df-4cd2-a334-f6572fe9c463", "type": "any", "enabled": true}, {"key": "owner", "value": "elry7", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "15", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "29", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590109", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "**************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "1", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2025-06", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "163", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "resource_scopes1", "value": "hdqtagosro", "type": "any", "enabled": true}, {"key": "MSISDN", "value": "***************", "type": "default", "enabled": true}, {"key": "file_key", "value": ["temp_file_key", "temp_file_key"], "type": "any", "enabled": true}, {"key": "Rule_Name", "value": "cSslhCVNBL", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 6584, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": "7b2c06c8-d63d-4de9-8851-10c646d3fa0c", "type": "any", "enabled": true}, {"key": "notification_id", "value": "684815c6b055c89de2e29ce1", "type": "any", "enabled": true}, {"key": "work_id", "value": "67fcfeab549e7f47f0230652", "type": "any", "enabled": true}, {"key": "month_adt", "value": "2025-07", "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "dataVolume", "value": "null", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "c3d0f796-18cf-4d51-bef3-1e51a6d24780", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 5, "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-12", "type": "any", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590125", "type": "default", "enabled": true}, {"key": "MSISDN_notification", "value": "883200000110322", "type": "default", "enabled": true}, {"key": "ModelID", "value": 45, "type": "any", "enabled": true}, {"key": "rateplan_id", "value": 322, "type": "any", "enabled": true}, {"key": "CDR_ID", "value": "684815bb3ea39ff0ae39c7b1", "type": "any", "enabled": true}, {"key": "imsi_notification", "value": "***************", "type": "any", "enabled": true}, {"key": "requestTimeout", "value": "true", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IoMZze_R-rO9ncuD2AhW3VGdgqUd7Tl8DNboXKdogY7F-DUYoU8NWplRGnTWGk3ywuTg_FLDwmjBnKfMsdLstOe0-LSc8xo0XvvwgRUaKKIQJnt-KUDDU2TewGaZTxSN8vzAEH4-XzyPc0GHdoxMZXr3KG3sJTT6xWrSy9U49Nbfo7eNBy_MYtEHZkQPnFUmUBQxdm7Tszhk2MkZ8ZlOdfPwwCpW1fJU0Tjj48XU3ap5A0XF9qZj9HKYYKQhTcS6z4zmTeS2XKfgKyw_zx9Fzd_sHHkFLektTZL5QmNDmLiv8chSTUQcxw__9hOj7WyHFTcGZmChUsgs41oUy5jVdg", "type": "secret", "enabled": true}, {"key": "randomTime", "value": "16:47:25", "type": "any", "enabled": true}, {"key": "rateplanid_1", "value": 332, "type": "any", "enabled": true}, {"key": "Target_RatePlan", "value": 50, "type": "any", "enabled": true}, {"key": "Source_RatePlan", "value": 21, "type": "any", "enabled": true}, {"key": "rateplanid_def", "value": 333, "type": "any", "enabled": true}, {"key": "account_id_rp_change", "value": "3", "type": "default", "enabled": true}, {"key": "imsi_rp_change", "value": "***************", "type": "default", "enabled": true}, {"key": "current_hours", "value": "11", "type": "any", "enabled": true}, {"key": "current_minutes", "value": "23", "type": "any", "enabled": true}, {"key": "current_seconds", "value": "43", "type": "any", "enabled": true}, {"key": "Account_id", "value": 836, "type": "default", "enabled": true}, {"key": "rule_Definition_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_Definition_id_2", "value": "4", "type": "default", "enabled": true}, {"key": "Token", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O1ykgBhTMEaaRSHFzhVDrwZvhD7JsiCm2gPI1beusNxiEmCuud7EQpKJvUyFWWGwVuWvzqmxlQmK0f1Jck8Uu0OQN7OCIIYJQ7adYUL2VoqsN2kfoVq2rc9Y1uM3ljkMe6rtUZ-kplT_pyJelBkVnWmTDvVL0j96nj7okmom9ptWHvaiOENw8FP5CCPfKJRVKPbsMlQt_Se3ZZ8Zfmgc85j4UV-EVrFfkjzjtzOyqKcBtd8lL1JUvFzI1jmdg4mAoMIliTj9GeLm-XOZQRHu9J-42B85wuNQYtX256Qhr2qsDna_o4j9c18b8HEbZLCGh8iClrS8_w8bQNw9xLt6gw", "type": "any", "enabled": true}, {"key": "IMSI_Audit", "value": "234588570010002", "type": "default", "enabled": true}, {"key": "trace_id", "value": "682c117238e28b955e803648", "type": "any", "enabled": true}, {"key": "RequestID", "value": "687608165ae8fb189b18359a", "type": "any", "enabled": true}, {"key": "user_id", "value": "4f239986-ad9e-4b02-81e2-b6036f0b3dc5", "type": "default", "enabled": true}, {"key": "rule_rely_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_rely_id_2", "value": "2", "type": "default", "enabled": true}, {"key": "MSISDN_Audit", "value": "************", "type": "default", "enabled": true}, {"key": "account_id_audit", "value": "13", "type": "default", "enabled": true}, {"key": "account_log_id", "value": "684c02f03c32af287431bce6", "type": "any", "enabled": true}, {"key": "order_id", "value": "d664cc8b-8dcd-49c1-b1a7-9451ca9162fa", "type": "any", "enabled": true}, {"key": "policy_id", "value": "6ad0d1fa-0b71-4de4-93d2-183a9a04bfa6", "type": "any", "enabled": true}, {"key": "request_id", "value": "6876414ab4997c907d379472", "type": "any", "enabled": true}, {"key": "email_random", "value": "zs9ml5", "type": "any", "enabled": true}, {"key": "Org_Id", "value": "3", "type": "default", "enabled": true}, {"key": "User_Id", "value": "6f7a0ed0-1d61-414d-aae0-c4f3e9769cfd", "type": "any", "enabled": true}, {"key": "IMSI_Location", "value": "***************", "type": "default", "enabled": true}, {"key": "pmns1", "value": "KKZ6R", "type": "any", "enabled": true}, {"key": "pmns2", "value": "774B5", "type": "any", "enabled": true}, {"key": "domain1", "value": "xwuzlj.example.com", "type": "any", "enabled": true}, {"key": "domain2", "value": "1h1zzc.example.com", "type": "any", "enabled": true}, {"key": "client_name", "value": "Satterfield - Schamberger", "type": "any", "enabled": true}, {"key": "Orgclient_Id", "value": 927, "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-24T05:47:20.664Z", "_postman_exported_using": "Postman/11.55.3"}