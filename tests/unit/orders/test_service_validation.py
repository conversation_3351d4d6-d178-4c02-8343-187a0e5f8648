"""Test cases for orders service validation logic."""

from datetime import datetime
from unittest.mock import MagicMock
from uuid import uuid4

import pytest

import app.config
from api.orders.schemas import TrackingInfo, UpdateOrderStatusRequest
from common.parser import ParsingError
from common.utils import set_trace_id
from orders.constants import OrderStatus
from orders.domain import model
from orders.services import OrdersService
from sim.exceptions import NotFound

set_trace_id(uuid4())


class TestOrderStatusValidation:
    """Test order status transition validation logic."""

    @pytest.fixture
    def mock_repository(self):
        return MagicMock()

    @pytest.fixture
    def mock_mail_service(self):
        return MagicMock()

    @pytest.fixture
    def mock_audit_service(self):
        from sim.adapters.externalapi import FakeAuditServiceAPI

        return FakeAuditServiceAPI()

    @pytest.fixture
    def orders_service(self, mock_repository, mock_mail_service, mock_audit_service):
        return OrdersService(
            orders_repository=mock_repository,
            mail_service=mock_mail_service,
            audit_service=mock_audit_service,
        )

    @pytest.fixture(autouse=True)
    def set_bt_manager_mail_id(self, monkeypatch):
        # Remove quotes if present, as settings.BT_MANAGER_MAIL_ID may strip them
        monkeypatch.setenv("BT_MANAGER_MAIL_ID", "<EMAIL>")
        # Also patch settings if imported directly

        app.config.settings.BT_MANAGER_MAIL_ID = "<EMAIL>"

    def test_valid_status_transitions_from_pending(
        self, orders_service, mock_repository
    ):
        """Test valid status transitions from PENDING."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.PENDING.value
        mock_repository.get_order.return_value = mock_order
        mock_repository.update_order_status.return_value = MagicMock(id=order_id)
        mock_repository.get_order_details.return_value = (
            self._create_mock_order_details(order_id)
        )

        # Test PENDING -> APPROVED
        update_data = UpdateOrderStatusRequest(status=OrderStatus.APPROVED.value)
        result = orders_service.update_order_status(
            order_uuid=order_id, update_data=update_data, client_ip="127.0.0.1"
        )
        assert result.id == order_id

        # Test PENDING -> CANCELLED
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.CANCELLED.value, comments="Invalid order"
        )
        result = orders_service.update_order_status(
            order_uuid=order_id,
            update_data=update_data,
            client_ip="127.0.0.1",
        )
        assert result.id == order_id

    def test_valid_status_transitions_from_approved(
        self, orders_service, mock_repository
    ):
        """Test valid status transitions from APPROVED."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.APPROVED.value
        mock_repository.get_order.return_value = mock_order
        mock_repository.update_order_status.return_value = MagicMock(id=order_id)
        mock_repository.get_order_details.return_value = (
            self._create_mock_order_details(order_id)
        )

        # Test APPROVED -> SHIPPED
        tracking = TrackingInfo(
            reference_id="TRACK123",
            reference_url="https://tracking.example.com/TRACK123",
        )
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.SHIPPED.value, tracking=tracking
        )
        result = orders_service.update_order_status(
            order_uuid=order_id, update_data=update_data, client_ip="127.0.0.1"
        )
        assert result.id == order_id

        # Test APPROVED -> CANCELLED
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.CANCELLED.value, comments="Changed requirements"
        )
        result = orders_service.update_order_status(
            order_uuid=order_id, update_data=update_data, client_ip="127.0.0.1"
        )
        assert result.id == order_id

    def test_invalid_status_transitions_from_pending(
        self, orders_service, mock_repository
    ):
        """Test invalid status transitions from PENDING."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.PENDING.value
        mock_repository.get_order.return_value = mock_order

        # Test PENDING -> SHIPPED (invalid)
        update_data = UpdateOrderStatusRequest(status=OrderStatus.SHIPPED.value)

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Invalid status transition from PENDING to SHIPPED" in str(
            exc_info.value
        )

    def test_invalid_status_transitions_from_approved(
        self, orders_service, mock_repository
    ):
        """Test invalid status transitions from APPROVED."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.APPROVED.value
        mock_repository.get_order.return_value = mock_order

        # Test APPROVED -> PENDING (invalid)
        update_data = UpdateOrderStatusRequest(status=OrderStatus.PENDING.value)

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Invalid status transition from APPROVED to PENDING" in str(
            exc_info.value
        )

    def test_no_transitions_from_shipped(self, orders_service, mock_repository):
        """Test that no transitions are allowed from SHIPPED."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.SHIPPED.value
        mock_repository.get_order.return_value = mock_order

        # Test SHIPPED -> any status (invalid)
        for status in [
            OrderStatus.PENDING,
            OrderStatus.APPROVED,
            OrderStatus.CANCELLED,
        ]:
            update_data = UpdateOrderStatusRequest(status=status.value)

            with pytest.raises(ParsingError) as exc_info:
                orders_service.update_order_status(order_id, update_data, "127.0.0.1")

            assert f"Invalid status transition from SHIPPED to {status.value}" in str(
                exc_info.value
            )

    def test_no_transitions_from_cancelled(self, orders_service, mock_repository):
        """Test that no transitions are allowed from CANCELLED."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.CANCELLED.value
        mock_repository.get_order.return_value = mock_order

        # Test CANCELLED -> any status (invalid)
        for status in [OrderStatus.PENDING, OrderStatus.APPROVED, OrderStatus.SHIPPED]:
            update_data = UpdateOrderStatusRequest(status=status.value)

            with pytest.raises(ParsingError) as exc_info:
                orders_service.update_order_status(order_id, update_data, "127.0.0.1")

            assert "Cannot update status from CANCELLED" in str(exc_info.value)

    def test_order_not_found(self, orders_service, mock_repository):
        """Test handling when order is not found."""
        order_id = uuid4()
        mock_repository.get_order.return_value = None

        update_data = UpdateOrderStatusRequest(status=OrderStatus.APPROVED.value)

        with pytest.raises(NotFound) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Data not found" in str(exc_info.value)

    def _create_mock_order_details(self, order_id):
        """Helper method to create mock order details."""
        return model.OrderDetailsResponse(
            id=1,
            order_id=order_id,
            order_uuid=str(order_id),
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status="PENDING",
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="MICRO", quantity=100)],
            order_tracking=None,
        )


class TestTrackingValidation:
    """Test tracking information validation logic."""

    @pytest.fixture
    def mock_repository(self):
        return MagicMock()

    @pytest.fixture
    def mock_mail_service(self):
        return MagicMock()

    @pytest.fixture
    def mock_audit_service(self):
        from sim.adapters.externalapi import FakeAuditServiceAPI

        return FakeAuditServiceAPI()

    @pytest.fixture
    def orders_service(self, mock_repository, mock_mail_service, mock_audit_service):
        return OrdersService(
            orders_repository=mock_repository,
            mail_service=mock_mail_service,
            audit_service=mock_audit_service,
        )

    def test_tracking_required_for_shipped_status(
        self, orders_service, mock_repository
    ):
        """Test that tracking information is required for SHIPPED status."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.APPROVED.value
        mock_repository.get_order.return_value = mock_order

        # Test SHIPPED without tracking
        update_data = UpdateOrderStatusRequest(status=OrderStatus.SHIPPED.value)

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Tracking information is required for shipped orders" in str(
            exc_info.value
        )

    def test_tracking_reference_id_required_for_shipped(
        self, orders_service, mock_repository
    ):
        """Test that tracking reference ID is required for SHIPPED status."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.APPROVED.value
        mock_repository.get_order.return_value = mock_order

        # Test SHIPPED with tracking but no reference_id
        tracking = TrackingInfo(reference_url="https://tracking.example.com/TRACK123")
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.SHIPPED.value, tracking=tracking
        )

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Tracking reference ID is required for shipped orders" in str(
            exc_info.value
        )

    def test_tracking_reference_url_required_for_shipped(
        self, orders_service, mock_repository
    ):
        """Test that tracking reference URL is required for SHIPPED status."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.APPROVED.value
        mock_repository.get_order.return_value = mock_order

        # Test SHIPPED with tracking but no reference_url
        tracking = TrackingInfo(reference_id="TRACK123")
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.SHIPPED.value, tracking=tracking
        )

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Tracking reference URL is required for shipped orders" in str(
            exc_info.value
        )

    def test_tracking_not_allowed_for_non_shipped_status(
        self, orders_service, mock_repository
    ):
        """Test that tracking information is not allowed for non-SHIPPED status."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.PENDING.value
        mock_repository.get_order.return_value = mock_order

        # Test APPROVED with tracking (not allowed)
        tracking = TrackingInfo(
            reference_id="TRACK123",
            reference_url="https://tracking.example.com/TRACK123",
        )
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.APPROVED.value, tracking=tracking
        )

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Tracking information is only allowed for shipped orders" in str(
            exc_info.value
        )


class TestCommentsValidation:
    """Test comments validation logic."""

    @pytest.fixture
    def mock_repository(self):
        return MagicMock()

    @pytest.fixture
    def mock_mail_service(self):
        return MagicMock()

    @pytest.fixture
    def mock_audit_service(self):
        from sim.adapters.externalapi import FakeAuditServiceAPI

        return FakeAuditServiceAPI()

    @pytest.fixture
    def orders_service(self, mock_repository, mock_mail_service, mock_audit_service):
        return OrdersService(
            orders_repository=mock_repository,
            mail_service=mock_mail_service,
            audit_service=mock_audit_service,
        )

    def test_comments_required_for_cancelled_status(
        self, orders_service, mock_repository
    ):
        """Test that comments are required for CANCELLED status."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.PENDING.value
        mock_repository.get_order.return_value = mock_order

        # Test CANCELLED without comments
        update_data = UpdateOrderStatusRequest(status=OrderStatus.CANCELLED.value)

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")
        assert "Comments are required for orders with status CANCELLED" in str(
            exc_info.value
        )

    def test_comments_not_allowed_for_non_cancelled_status(
        self, orders_service, mock_repository
    ):
        """Test that comments are not allowed for non-CANCELLED status."""
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.status = OrderStatus.PENDING.value
        mock_repository.get_order.return_value = mock_order

        # Test APPROVED with comments (not allowed)
        update_data = UpdateOrderStatusRequest(
            status=OrderStatus.APPROVED.value, comments="Some comment"
        )

        with pytest.raises(ParsingError) as exc_info:
            orders_service.update_order_status(order_id, update_data, "127.0.0.1")

        assert "Comments are only allowed for cancelled orders" in str(exc_info.value)


class TestEmailDataConstruction:
    """Test email data construction logic."""

    @pytest.fixture
    def mock_repository(self):
        return MagicMock()

    @pytest.fixture
    def mock_mail_service(self):
        return MagicMock()

    @pytest.fixture
    def mock_audit_service(self):
        from sim.adapters.externalapi import FakeAuditServiceAPI

        return FakeAuditServiceAPI()

    @pytest.fixture
    def orders_service(self, mock_repository, mock_mail_service, mock_audit_service):
        return OrdersService(
            orders_repository=mock_repository,
            mail_service=mock_mail_service,
            audit_service=mock_audit_service,
        )

    def test_email_data_construction_with_tracking(
        self, orders_service, mock_repository
    ):
        """Test email data construction with tracking information."""
        order_id = uuid4()

        # Create mock order details with tracking
        tracking = model.OrderTracking(
            reference_id="TRACK123",
            reference_url="https://tracking.example.com/TRACK123",
        )

        order_details = model.OrderDetailsResponse(
            id=1,
            order_id="ORD-**************-123456",
            order_uuid=str(order_id),
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status=OrderStatus.SHIPPED.value,
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_reference="REF123",
                person_placing_order="Jane Doe",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="Apt 4B",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
                other_information="Leave at door",
            ),
            order_items=[
                model.OrderItem(sim_type="MICRO", quantity=100),
                model.OrderItem(sim_type="NANO", quantity=50),
            ],
            order_tracking=tracking,
            comments="Order shipped successfully",
        )

        mock_repository.get_order_details.return_value = order_details

        # Call the private method to test email data construction
        order_data = orders_service._OrdersService__constructing_order_data_for_mail(
            order_id
        )

        assert order_data.order_id == "ORD-**************-123456"
        assert order_data.order_status == OrderStatus.SHIPPED.value
        assert order_data.order_date == "2024-01-01 12:00"
        assert order_data.sim_type_wise_qty == {"MICRO": 100, "NANO": 50}
        assert order_data.customer_account_ref == "REF123"
        assert order_data.person_name == "Jane Doe"
        assert order_data.person_email == "<EMAIL>"
        assert order_data.phone_number == "**********"
        assert order_data.shipping_address_address1 == "123 Test St"
        assert order_data.shipping_address_address2 == "Apt 4B"
        assert order_data.city == "Test City"
        assert order_data.state == "Test State"
        assert order_data.postal_code == "12345"
        assert order_data.country == "Test Country"
        assert order_data.additional_info == "Leave at door"
        assert order_data.comments == "Order shipped successfully"
        assert order_data.tracking_url == "https://tracking.example.com/TRACK123"
        assert order_data.tracking_ref == "TRACK123"

    def test_email_data_construction_without_tracking(
        self, orders_service, mock_repository
    ):
        """Test email data construction without tracking information."""
        order_id = uuid4()

        # Create mock order details without tracking
        order_details = model.OrderDetailsResponse(
            id=1,
            order_id="ORD-**************-123456",
            order_uuid=str(order_id),
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status=OrderStatus.PENDING.value,
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_reference="REF123",
                person_placing_order="Jane Doe",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="MICRO", quantity=100)],
            order_tracking=None,
            comments=None,
        )

        mock_repository.get_order_details.return_value = order_details

        # Call the private method to test email data construction
        order_data = orders_service._OrdersService__constructing_order_data_for_mail(
            order_id
        )

        assert order_data.order_id == "ORD-**************-123456"
        assert order_data.order_status == OrderStatus.PENDING.value
        assert order_data.sim_type_wise_qty == {"MICRO": 100}
        assert order_data.tracking_url is None
        assert order_data.tracking_ref is None
        assert order_data.comments is None


class TestCreateOrderValidation:
    """Test create order validation and processing."""

    @pytest.fixture
    def mock_repository(self):
        return MagicMock()

    @pytest.fixture
    def mock_mail_service(self):
        return MagicMock()

    @pytest.fixture
    def mock_audit_service(self):
        from sim.adapters.externalapi import FakeAuditServiceAPI

        return FakeAuditServiceAPI()

    @pytest.fixture
    def orders_service(self, mock_repository, mock_mail_service, mock_audit_service):
        return OrdersService(
            orders_repository=mock_repository,
            mail_service=mock_mail_service,
            audit_service=mock_audit_service,
        )

    def test_create_order_success_flow(self, orders_service, mock_repository):
        """Test successful order creation flow."""
        order_id = uuid4()
        customer_id = uuid4()

        # Create order request
        order_request = model.OrderRequest(
            order_by="test_user",
            customer_details=model.OrderCustomer(
                customer_id=customer_id,
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_id=1,
                customer_account_name="Test Account",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="MICRO", quantity=100)],
        )

        # Mock repository responses
        order_response = model.OrderResponse(id=order_id)
        mock_repository.create_order.return_value = order_response
        mock_repository.get_order_details.return_value = (
            self._create_mock_order_details(order_id)
        )

        # Call create_order
        result = orders_service.create_order(order_request, "test_user")

        # Verify the flow
        assert result.id == order_id
        mock_repository.create_order.assert_called_once_with(order=order_request)
        mock_repository.add_order_customer.assert_called_once()
        mock_repository.add_order_shipping.assert_called_once()
        mock_repository.add_order_item.assert_called_once()
        mock_repository.commit_order.assert_called_once()

    def test_create_order_no_response_id(
        self,
        orders_service,
        mock_repository,
    ):
        """Test create order when no response ID is returned."""
        customer_id = uuid4()

        # Create order request
        order_request = model.OrderRequest(
            order_by="test_user",
            customer_details=model.OrderCustomer(
                customer_id=customer_id,
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="MICRO", quantity=100)],
        )

        # Mock repository to return response without ID
        order_response = model.OrderResponse(id=None)
        mock_repository.create_order.return_value = order_response

        # Call create_order and expect ParsingError
        with pytest.raises(ParsingError) as exc_info:
            orders_service.create_order(order_request, "test_user")

        assert "Failed to create order, please try again" in str(exc_info.value)

    def _create_mock_order_details(self, order_id):
        """Helper method to create mock order details."""
        return model.OrderDetailsResponse(
            id=1,
            order_id="ORD-**************-123456",
            order_uuid=str(order_id),
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status="PENDING",
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="MICRO", quantity=100)],
            order_tracking=None,
        )
